using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.ValueObjects;
using SubscriptionManagement.Infrastructure.Persistence;

namespace SubscriptionManagement.Infrastructure.Data;

public static class SeedData
{
    public static async Task SeedAsync(SubscriptionDbContext context)
    {
        if (await context.Plans.AnyAsync())
        {
            return; // Database has been seeded
        }

        // Seed Plans
        var plans = CreateSamplePlans();
        await context.Plans.AddRangeAsync(plans);

        // Seed Plan Features
        var planFeatures = CreatePlanFeatures(plans);
        await context.PlanFeatures.AddRangeAsync(planFeatures);

        // Seed Feature Flags
        var featureFlags = CreateFeatureFlags();
        await context.FeatureFlags.AddRangeAsync(featureFlags);

        // Seed Subscriptions
        var subscriptions = CreateSampleSubscriptions(plans);
        await context.Subscriptions.AddRangeAsync(subscriptions);

        await context.SaveChangesAsync();
    }

    private static List<Plan> CreateSamplePlans()
    {
        var plans = new List<Plan>();

        // Basic Plan for Transport Companies
        var basicPlan = new Plan(
            "Basic Transport",
            "Basic plan for small transport companies",
<<<<<<< HEAD
            1,
=======
            "1.0",
>>>>>>> 302578b24b8f45910ea5f40a81721ed56b9261d1
            PlanType.Basic,
            UserType.TransportCompany,
            new Money(2999, "INR"),
            BillingCycle.Monthly(),
            PlanLimits.ForTransportCompany(10)
        );

        // Pro Plan for Transport Companies
        var proPlan = new Plan(
            "Pro Transport",
            "Professional plan for growing transport companies",
<<<<<<< HEAD
            1,
=======
            "1.0",
>>>>>>> 302578b24b8f45910ea5f40a81721ed56b9261d1
            PlanType.Pro,
            UserType.TransportCompany,
            new Money(7999, "INR"),
            BillingCycle.Monthly(),
            PlanLimits.ForTransportCompany(50)
        );

        // Enterprise Plan for Transport Companies
        var enterprisePlan = new Plan(
            "Enterprise Transport",
            "Enterprise plan for large transport companies",
<<<<<<< HEAD
            1,
=======
            "1.0",
>>>>>>> 302578b24b8f45910ea5f40a81721ed56b9261d1
            PlanType.Enterprise,
            UserType.TransportCompany,
            new Money(19999, "INR"),
            BillingCycle.Monthly(),
            PlanLimits.Unlimited()
        );

        // Basic Plan for Brokers
        var brokerBasicPlan = new Plan(
            "Basic Broker",
            "Basic plan for freight brokers",
<<<<<<< HEAD
            1,
=======
            "1.0",
>>>>>>> 302578b24b8f45910ea5f40a81721ed56b9261d1
            PlanType.Basic,
            UserType.Broker,
            new Money(1999, "INR"),
            BillingCycle.Monthly(),
            PlanLimits.ForBroker(20, 50)
        );

        // Pro Plan for Brokers
        var brokerProPlan = new Plan(
            "Pro Broker",
            "Professional plan for freight brokers",
<<<<<<< HEAD
            1,
=======
            "1.0",
>>>>>>> 302578b24b8f45910ea5f40a81721ed56b9261d1
            PlanType.Pro,
            UserType.Broker,
            new Money(4999, "INR"),
            BillingCycle.Monthly(),
            PlanLimits.ForBroker(100, 200)
        );

        // Basic Plan for Carriers
        var carrierBasicPlan = new Plan(
            "Basic Carrier",
            "Basic plan for carriers",
<<<<<<< HEAD
            1,
=======
            "1.0",
>>>>>>> 302578b24b8f45910ea5f40a81721ed56b9261d1
            PlanType.Basic,
            UserType.Carrier,
            new Money(999, "INR"),
            BillingCycle.Monthly(),
            PlanLimits.ForCarrier(15, 3)
        );

        // Pro Plan for Carriers
        var carrierProPlan = new Plan(
            "Pro Carrier",
            "Professional plan for carriers",
<<<<<<< HEAD
            1,
=======
            "1.0",
>>>>>>> 302578b24b8f45910ea5f40a81721ed56b9261d1
            PlanType.Pro,
            UserType.Carrier,
            new Money(2999, "INR"),
            BillingCycle.Monthly(),
            PlanLimits.ForCarrier(50, 15)
        );

        plans.AddRange(new[] { basicPlan, proPlan, enterprisePlan, brokerBasicPlan, brokerProPlan, carrierBasicPlan, carrierProPlan });
        return plans;
    }

    private static List<PlanFeature> CreatePlanFeatures(List<Plan> plans)
    {
        var planFeatures = new List<PlanFeature>();

        // Add features to plans
        foreach (var plan in plans)
        {
            switch (plan.UserType)
            {
                case UserType.TransportCompany:
                    plan.AddFeature(Guid.NewGuid(), "RFQ Limit", "rfq", "Maximum number of RFQs allowed", true);
                    plan.AddFeature(Guid.NewGuid(), "Analytics", "analytics", "Access to analytics dashboard", true);
                    break;
                case UserType.Broker:
                    plan.AddFeature(Guid.NewGuid(), "RFQ Limit", "rfq", "Maximum number of RFQs allowed", true);
                    plan.AddFeature(Guid.NewGuid(), "Carrier Limit", "carrier", "Maximum number of carriers allowed", true);
                    break;
                case UserType.Carrier:
                    plan.AddFeature(Guid.NewGuid(), "RFQ Limit", "rfq", "Maximum number of RFQs allowed", true);
                    plan.AddFeature(Guid.NewGuid(), "Vehicle Limit", "vehicle", "Maximum number of vehicles allowed", true);
                    break;
            }

            planFeatures.AddRange(plan.Features);
        }

        return planFeatures;
    }

    private static List<FeatureFlag> CreateFeatureFlags()
    {
        var featureFlags = new List<FeatureFlag>();

        // Feature flag for new dashboard
        var newDashboardFlag = new FeatureFlag(
            "New Dashboard V2",
            "Enable the new dashboard interface",
            "new_dashboard_v2",
            FeatureFlagType.Boolean,
            "false"
        );

        // Feature flag for AI-powered matching
        var aiMatchingFlag = new FeatureFlag(
            "AI Powered Matching",
            "Enable AI-powered load matching for brokers",
            "ai_powered_matching",
            FeatureFlagType.Boolean,
            "false"
        );

        // Feature flag for real-time tracking
        var realTimeTrackingFlag = new FeatureFlag(
            "Real-time Tracking",
            "Enable real-time GPS tracking for trips",
            "real_time_tracking",
            FeatureFlagType.Boolean,
            "true"
        );

        // Feature flag for mobile app v2
        var mobileAppV2Flag = new FeatureFlag(
            "Mobile App V2",
            "Enable the new mobile app interface",
            "mobile_app_v2",
            FeatureFlagType.Boolean,
            "false"
        );

        featureFlags.AddRange(new[] { newDashboardFlag, aiMatchingFlag, realTimeTrackingFlag, mobileAppV2Flag });
        return featureFlags;
    }

    private static List<Subscription> CreateSampleSubscriptions(List<Plan> plans)
    {
        var subscriptions = new List<Subscription>();

        // Sample User IDs (these would come from Identity service)
        var transportCompanyUserId1 = Guid.Parse("11111111-1111-1111-1111-111111111111");
        var transportCompanyUserId2 = Guid.Parse("*************-2222-2222-************");
        var brokerUserId1 = Guid.Parse("*************-3333-3333-************");
        var brokerUserId2 = Guid.Parse("*************-4444-4444-************");
        var carrierUserId1 = Guid.Parse("*************-5555-5555-************");
        var carrierUserId2 = Guid.Parse("*************-6666-6666-************");

        // Transport Company subscriptions
        var basicTransportPlan = plans.First(p => p.Name == "Basic Transport");
        var proTransportPlan = plans.First(p => p.Name == "Pro Transport");

        var subscription1 = new Subscription(
            transportCompanyUserId1,
            basicTransportPlan,
            DateTime.UtcNow.AddDays(-30), // Started 30 days ago
            null, // No trial
            true, // Auto-renew
            ProrationMode.Immediate
        );

        var subscription2 = new Subscription(
            transportCompanyUserId2,
            proTransportPlan,
            DateTime.UtcNow.AddDays(-15), // Started 15 days ago
            DateTime.UtcNow.AddDays(-8), // Trial ended 8 days ago
            true, // Auto-renew
            ProrationMode.NextBillingCycle
        );

        // Broker subscriptions
        var basicBrokerPlan = plans.First(p => p.Name == "Basic Broker");
        var proBrokerPlan = plans.First(p => p.Name == "Pro Broker");

        var subscription3 = new Subscription(
            brokerUserId1,
            basicBrokerPlan,
            DateTime.UtcNow.AddDays(-20), // Started 20 days ago
            null, // No trial
            true, // Auto-renew
            ProrationMode.Immediate
        );

        var subscription4 = new Subscription(
            brokerUserId2,
            proBrokerPlan,
            DateTime.UtcNow.AddDays(-5), // Started 5 days ago
            DateTime.UtcNow.AddDays(2), // Trial ends in 2 days
            false, // No auto-renew yet
            ProrationMode.NextBillingCycle
        );

        // Carrier subscriptions
        var basicCarrierPlan = plans.First(p => p.Name == "Basic Carrier");
        var proCarrierPlan = plans.First(p => p.Name == "Pro Carrier");

        var subscription5 = new Subscription(
            carrierUserId1,
            basicCarrierPlan,
            DateTime.UtcNow.AddDays(-10), // Started 10 days ago
            null, // No trial
            true, // Auto-renew
            ProrationMode.Immediate
        );

        var subscription6 = new Subscription(
            carrierUserId2,
            proCarrierPlan,
            DateTime.UtcNow.AddDays(-3), // Started 3 days ago
            DateTime.UtcNow.AddDays(4), // Trial ends in 4 days
            false, // No auto-renew yet
            ProrationMode.NextBillingCycle
        );

        subscriptions.AddRange(new[] { subscription1, subscription2, subscription3, subscription4, subscription5, subscription6 });
        return subscriptions;
    }
}
